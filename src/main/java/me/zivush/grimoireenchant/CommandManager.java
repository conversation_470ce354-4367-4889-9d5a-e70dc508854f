package me.zivush.grimoireenchant;

import me.zivush.grimoireenchant.mana.ManaCoreGUI;
import me.zivush.grimoireenchant.mana.ManaCoreManager;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Manages all commands for the plugin
 */
public class CommandManager implements CommandExecutor, TabCompleter {

    private final GrimoireEnchant plugin;
    private ManaCoreManager manaCoreManager;
    private ManaCoreGUI manaCoreGUI;

    /**
     * Constructor for CommandManager
     *
     * @param plugin The main plugin instance
     */
    public CommandManager(GrimoireEnchant plugin) {
        this.plugin = plugin;

        // Register tab completer
        plugin.getCommand("grimoireenchant").setTabCompleter(this);
    }

    /**
     * Set the mana core manager
     *
     * @param manaCoreManager The mana core manager
     */
    public void setManaCoreManager(ManaCoreManager manaCoreManager) {
        this.manaCoreManager = manaCoreManager;
    }

    /**
     * Set the mana core GUI
     *
     * @param manaCoreGUI The mana core GUI
     */
    public void setManaCoreGUI(ManaCoreGUI manaCoreGUI) {
        this.manaCoreGUI = manaCoreGUI;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Check if the command is /grimoireenchant or /ge
        if (command.getName().equalsIgnoreCase("grimoireenchant")) {
            // Check if there are any arguments
            if (args.length == 0) {
                sendUsage(sender);
                return true;
            }

            // Handle subcommands
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    handleGiveCommand(sender, args);
                    break;
                case "reload":
                    handleReloadCommand(sender);
                    break;
                case "core":
                    handleCoreCommand(sender, args);
                    break;

                default:
                    sendUsage(sender);
                    break;
            }

            return true;
        } else if (command.getName().equalsIgnoreCase("core")) {
            // Direct /core command - open the GUI
            if (!(sender instanceof Player)) {
                String playerOnlyMsg = ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.PlayerOnly", "&cThis command can only be executed by a player."));
                sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + playerOnlyMsg);
                return true;
            }

            Player player = (Player) sender;

            // Check permission
            if (!player.hasPermission("grimoireenchant.core")) {
                String noPermMsg = ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.NoPermission", "&cYou do not have permission to use this command."));
                sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + noPermMsg);
                return true;
            }

            // Open the GUI
            manaCoreGUI.openGui(player);
            return true;
        }

        return false;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (command.getName().equalsIgnoreCase("grimoireenchant")) {
            if (args.length == 1) {
                // First argument - subcommands
                completions.addAll(Arrays.asList("give", "reload", "core"));
            } else if (args.length == 2 && args[0].equalsIgnoreCase("give")) {
                // Second argument for give - enchantment names
                completions.addAll(plugin.getEnchantmentManager().getAllEnchantments().stream()
                        .map(GrimoireEnchantment::getId)
                        .collect(Collectors.toList()));
            } else if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
                // Third argument for give - player names
                completions.addAll(Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .collect(Collectors.toList()));
            } else if (args.length == 4 && args[0].equalsIgnoreCase("give")) {
                // Fourth argument for give - rarity suggestions
                completions.addAll(Arrays.asList("common", "uncommon", "rare", "epic", "legendary"));
            } else if (args.length == 5 && args[0].equalsIgnoreCase("give")) {
                // Fifth argument for give - amount suggestions
                completions.addAll(Arrays.asList("1", "5", "10", "64"));
            } else if (args.length == 2 && args[0].equalsIgnoreCase("core")) {
                // Second argument for core - core subcommands
                completions.addAll(Arrays.asList("give"));

                // Add 'open' only if the sender is a player
                if (sender instanceof Player) {
                    completions.add("open");
                }
            } else if (args.length == 3 && args[0].equalsIgnoreCase("core") && args[1].equalsIgnoreCase("give")) {
                // Third argument for core give - player names
                completions.addAll(Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .collect(Collectors.toList()));
            } else if (args.length == 4 && args[0].equalsIgnoreCase("core") && args[1].equalsIgnoreCase("give")) {
                // Fourth argument for core give - core IDs
                if (manaCoreManager != null) {
                    completions.addAll(manaCoreManager.getAllCoreIds());
                }
            }
        } else if (command.getName().equalsIgnoreCase("core")) {
            // No tab completions for /core command as it has no arguments
        }

        return completions.stream()
                .filter(completion -> completion.toLowerCase().startsWith(args[args.length - 1].toLowerCase()))
                .collect(Collectors.toList());
    }

    /**
     * Send usage information to a sender
     *
     * @param sender The command sender
     */
    private void sendUsage(CommandSender sender) {
        String prefix = ColorUtils.process(
                plugin.getMessagesConfig().getString("General.Prefix", ""));
        String usage = ColorUtils.process(
                plugin.getMessagesConfig().getString("Command.Usage", "&cUsage: /ge give <enchantment> <player> [rarity] [amount]"));
        String coreUsage = ColorUtils.process(
                plugin.getMessagesConfig().getString("Command.CoreUsage", "&cUsage: /ge core open | /ge core give <player> <core>"));

        sender.sendMessage(prefix + usage);
        sender.sendMessage(prefix + coreUsage);
    }

    /**
     * Handle the give command
     *
     * @param sender The command sender
     * @param args The command arguments
     */
    private void handleGiveCommand(CommandSender sender, String[] args) {
        // Check permission
        if (!sender.hasPermission("grimoireenchant.give")) {
            String noPermMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.NoPermission", "&cYou do not have permission to use this command."));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + noPermMsg);
            return;
        }

        // Check arguments
        if (args.length < 3) {
            sendUsage(sender);
            return;
        }

        String enchantId = args[1];
        String playerName = args[2];
        int amount = 1;
        String rarity = "common";

        // Parse rarity if provided
        if (args.length >= 4) {
            String rarityArg = args[3].toLowerCase();
            if (Arrays.asList("common", "uncommon", "rare", "epic", "legendary").contains(rarityArg)) {
                rarity = rarityArg;
            } else {
                // Try to parse as amount if it's not a valid rarity
                try {
                    amount = Integer.parseInt(args[3]);
                    if (amount <= 0) {
                        String invalidAmountMsg = ColorUtils.process(
                                plugin.getMessagesConfig().getString("General.InvalidAmount", "&cAmount must be a positive number."));
                        sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidAmountMsg);
                        return;
                    }
                } catch (NumberFormatException e) {
                    // Not a valid rarity or amount
                    String invalidRarityMsg = ColorUtils.process(
                            plugin.getMessagesConfig().getString("General.InvalidRarity", "&cInvalid rarity: &e{rarity}&c. Valid rarities: common, uncommon, rare, epic, legendary."));
                    sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidRarityMsg.replace("{rarity}", args[3]));
                    return;
                }
            }
        }

        // Parse amount if rarity was provided
        if (args.length >= 5) {
            try {
                amount = Integer.parseInt(args[4]);
                if (amount <= 0) {
                    String invalidAmountMsg = ColorUtils.process(
                            plugin.getMessagesConfig().getString("General.InvalidAmount", "&cAmount must be a positive number."));
                    sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidAmountMsg);
                    return;
                }
            } catch (NumberFormatException e) {
                String invalidAmountMsg = ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.InvalidAmount", "&cAmount must be a positive number."));
                sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidAmountMsg);
                return;
            }
        }

        // Get the enchantment
        GrimoireEnchantment enchantment = plugin.getEnchantmentManager().getEnchantment(enchantId);
        if (enchantment == null) {
            String invalidEnchantMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.InvalidEnchantment", "&cEnchantment &e{enchant} &cnot found.")
                            .replace("{enchant}", enchantId));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidEnchantMsg);
            return;
        }

        // Get the player
        Player player = Bukkit.getPlayer(playerName);
        if (player == null) {
            String invalidPlayerMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.InvalidPlayer", "&cPlayer &e{player} &cnot found.")
                            .replace("{player}", playerName));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidPlayerMsg);
            return;
        }

        // Rarity has already been parsed

        // Create the enchantment book
        ItemStack book = enchantment.createEnchantmentBook(rarity);
        if (book == null) {
            String invalidEnchantMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.InvalidEnchantment", "&cEnchantment &e{enchant} &cnot found.")
                            .replace("{enchant}", enchantId));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidEnchantMsg);
            return;
        }

        // Set the amount
        book.setAmount(amount);

        // Give the book to the player
        player.getInventory().addItem(book);

        // Send success messages
        String giveSuccessMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("Command.GiveSuccess", "&aGave &e{amount}x {enchant} &ato &e{player}&a.")
                        .replace("{amount}", String.valueOf(amount))
                        .replace("{enchant}", enchantment.getDisplay())
                        .replace("{player}", player.getName()));
        sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + giveSuccessMsg);

        String receiveSuccessMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("Command.ReceiveSuccess", "&aYou received &e{amount}x {enchant}&a.")
                        .replace("{amount}", String.valueOf(amount))
                        .replace("{enchant}", enchantment.getDisplay()));
        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + receiveSuccessMsg);
    }

    /**
     * Handle the reload command
     *
     * @param sender The command sender
     */
    private void handleReloadCommand(CommandSender sender) {
        // Check permission
        if (!sender.hasPermission("grimoireenchant.reload")) {
            String noPermMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.NoPermission", "&cYou do not have permission to use this command."));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + noPermMsg);
            return;
        }

        // Reload configs
        plugin.reloadConfigs();

        // Send success message
        String reloadSuccessMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("General.ReloadSuccess", "&aConfiguration files have been reloaded successfully."));
        sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + reloadSuccessMsg);
    }

    /**
     * Handle the core command
     *
     * @param sender The command sender
     * @param args The command arguments
     */
    private void handleCoreCommand(CommandSender sender, String[] args) {
        // Check if mana core manager is initialized
        if (manaCoreManager == null || manaCoreGUI == null) {
            sender.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    "&cMana core system is not initialized properly."));
            return;
        }

        // Check if there are enough arguments
        if (args.length < 2) {
            String coreUsage = ColorUtils.process(
                    plugin.getMessagesConfig().getString("Command.CoreUsage", "&cUsage: /ge core open | /ge core give <player> <core>"));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + coreUsage);
            return;
        }

        String subCommand = args[1].toLowerCase();

        switch (subCommand) {
            case "open":
                handleCoreOpenCommand(sender);
                break;
            case "give":
                handleCoreGiveCommand(sender, args);
                break;
            default:
                String coreUsage = ColorUtils.process(
                        plugin.getMessagesConfig().getString("Command.CoreUsage", "&cUsage: /ge core open | /ge core give <player> <core>"));
                sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + coreUsage);
                break;
        }
    }

    /**
     * Handle the core open command
     *
     * @param sender The command sender
     */
    private void handleCoreOpenCommand(CommandSender sender) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            String playerOnlyMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.PlayerOnly", "&cThis command can only be executed by a player."));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + playerOnlyMsg);
            return;
        }

        // Check permission
        if (!sender.hasPermission("grimoireenchant.core")) {
            String noPermMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.NoPermission", "&cYou do not have permission to use this command."));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + noPermMsg);
            return;
        }

        // Open the core GUI
        Player player = (Player) sender;
        manaCoreGUI.openGui(player);
    }



    /**
     * Handle the core give command
     *
     * @param sender The command sender
     * @param args The command arguments
     */
    private void handleCoreGiveCommand(CommandSender sender, String[] args) {
        // Check permission
        if (!sender.hasPermission("grimoireenchant.core.give")) {
            String noPermMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.NoPermission", "&cYou do not have permission to use this command."));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + noPermMsg);
            return;
        }

        // Check if there are enough arguments
        if (args.length < 4) {
            String coreGiveUsage = ColorUtils.process(
                    plugin.getMessagesConfig().getString("Command.CoreGiveUsage", "&cUsage: /ge core give <player> <core>"));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + coreGiveUsage);
            return;
        }

        String playerName = args[2];
        String coreId = args[3];

        // Get the player
        Player player = Bukkit.getPlayer(playerName);
        if (player == null) {
            String invalidPlayerMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.InvalidPlayer", "&cPlayer &e{player} &cnot found.")
                            .replace("{player}", playerName));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidPlayerMsg);
            return;
        }

        // Check if the core exists
        if (!manaCoreManager.getAllCoreIds().contains(coreId)) {
            String invalidCoreMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("ManaCores.InvalidCore", "&cMana core &e{core} &cnot found.")
                            .replace("{core}", coreId));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidCoreMsg);
            return;
        }

        // Create physical core item
        ItemStack physicalCore = manaCoreManager.createPhysicalCoreItem(coreId);
        if (physicalCore == null) {
            String invalidCoreMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("ManaCores.InvalidCore", "&cMana core &e{core} &cnot found.")
                            .replace("{core}", coreId));
            sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + invalidCoreMsg);
            return;
        }
        if (physicalCore.hasItemMeta()) {
            ItemMeta meta = physicalCore.getItemMeta();
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            physicalCore.setItemMeta(meta);
        }
        // Give the physical core item to the player
        if (player.getInventory().firstEmpty() == -1) {
            // Inventory is full, drop the item
            player.getWorld().dropItemNaturally(player.getLocation(), physicalCore);
            String inventoryFullMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.InventoryFull", "&cYour inventory is full! The item was dropped on the ground."));
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + inventoryFullMsg);
        } else {
            player.getInventory().addItem(physicalCore);
        }

        // Send success messages
        String giveSuccessMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("ManaCores.GiveSuccess", "&aGave &e{core} &amana core to &e{player}&a.")
                        .replace("{core}", coreId)
                        .replace("{player}", player.getName()));
        sender.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + giveSuccessMsg);

        String receiveSuccessMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("ManaCores.ReceiveSuccess", "&aYou received the &e{core} &amana core.")
                        .replace("{core}", coreId));
        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + receiveSuccessMsg);
    }
}
