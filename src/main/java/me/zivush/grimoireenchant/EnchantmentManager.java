package me.zivush.grimoireenchant;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import me.zivush.grimoireenchant.mana.ManaCoreManager;
import me.zivush.grimoireenchant.utils.ColorUtils;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.PrepareAnvilEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;

/**
 * Manages all enchantments and NBT data
 */
public class EnchantmentManager implements Listener {

    private final GrimoireEnchant plugin;
    private final Map<String, GrimoireEnchantment> enchantments = new HashMap<>();
    private final Map<UUID, Integer> playerMana = new HashMap<>();
    private final Map<UUID, BossBar> manaBars = new HashMap<>();
    private final Map<UUID, Long> lastManaMessageTime = new HashMap<>();
    private ManaCoreManager manaCoreManager;

    // NBT Keys
    private final NamespacedKey enchantKey;
    private final NamespacedKey rarityKey;

    // Mana display type enum
    public enum ManaDisplayType {
        BOSS_BAR,
        ACTION_BAR,
        NONE
    }

    /**
     * Constructor for EnchantmentManager
     *
     * @param plugin The main plugin instance
     */
    public EnchantmentManager(GrimoireEnchant plugin) {
        this.plugin = plugin;
        this.enchantKey = new NamespacedKey(plugin, "grimoire_enchant");
        this.rarityKey = new NamespacedKey(plugin, "grimoire_rarity");

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // Start mana regeneration task
        startManaRegenTask();
    }

    /**
     * Set the mana core manager
     *
     * @param manaCoreManager The mana core manager
     */
    public void setManaCoreManager(ManaCoreManager manaCoreManager) {
        this.manaCoreManager = manaCoreManager;
    }

    /**
     * Register an enchantment
     *
     * @param enchantment The enchantment to register
     */
    public void registerEnchantment(GrimoireEnchantment enchantment) {
        enchantments.put(enchantment.getId().toLowerCase(), enchantment);
    }

    /**
     * Get an enchantment by ID
     *
     * @param id The enchantment ID
     * @return The enchantment, or null if not found
     */
    public GrimoireEnchantment getEnchantment(String id) {
        return enchantments.get(id.toLowerCase());
    }

    /**
     * Get all registered enchantments
     *
     * @return A collection of all enchantments
     */
    public Collection<GrimoireEnchantment> getAllEnchantments() {
        return enchantments.values();
    }

    /**
     * Set enchantment data on an item
     *
     * @param item The item to set data on
     * @param enchantId The enchantment ID
     * @param rarity The enchantment rarity
     * @return The modified item
     */
    public ItemStack setEnchantmentData(ItemStack item, String enchantId, String rarity) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        container.set(enchantKey, PersistentDataType.STRING, enchantId);
        container.set(rarityKey, PersistentDataType.STRING, rarity);

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Check if an item has a Grimoire enchantment
     *
     * @param item The item to check
     * @return True if the item has an enchantment
     */
    public boolean hasEnchantment(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return false;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return false;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        return container.has(enchantKey, PersistentDataType.STRING);
    }

    /**
     * Get the enchantment ID from an item
     *
     * @param item The item to get the enchantment from
     * @return The enchantment ID, or null if not found
     */
    public String getEnchantmentId(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return null;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return null;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        return container.has(enchantKey, PersistentDataType.STRING) ?
               container.get(enchantKey, PersistentDataType.STRING) : null;
    }

    /**
     * Get the enchantment rarity from an item
     *
     * @param item The item to get the rarity from
     * @return The enchantment rarity, or null if not found
     */
    public String getEnchantmentRarity(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return null;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return null;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        return container.has(rarityKey, PersistentDataType.STRING) ?
               container.get(rarityKey, PersistentDataType.STRING) : null;
    }

    /**
     * Apply an enchantment to an item
     *
     * @param item The item to apply the enchantment to
     * @param enchantBook The enchantment book
     * @return The enchanted item
     */
    public ItemStack applyEnchantment(ItemStack item, ItemStack enchantBook) {
        if (item == null || enchantBook == null) return item;

        String enchantId = getEnchantmentId(enchantBook);
        String rarity = getEnchantmentRarity(enchantBook);

        if (enchantId == null || rarity == null) return item;

        GrimoireEnchantment enchantment = getEnchantment(enchantId);
        if (enchantment == null) return item;

        // Create a clone of the item to avoid modifying the original directly
        ItemStack result = item.clone();

        // Apply enchantment data
        ItemMeta meta = result.getItemMeta();
        if (meta == null) return result;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        container.set(enchantKey, PersistentDataType.STRING, enchantId);
        container.set(rarityKey, PersistentDataType.STRING, rarity);

        // Add lore
        List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();

        // Format from config
        String bookFormat = plugin.getConfig().getString("Book-Format", "{rarity} {display} &#A9A9A9- {ability}");
        String rarityDisplay = plugin.getConfig().getString("Rarity." + rarity, rarity);

        bookFormat = bookFormat.replace("{rarity}", rarityDisplay)
                             .replace("{display}", enchantment.getDisplay())
                             .replace("{ability}", enchantId);

        lore.add(ColorUtils.process(bookFormat));
        meta.setLore(lore);

        // Add enchantment glow if not already present
        if (!meta.hasEnchants()) {
            meta.addEnchant(org.bukkit.enchantments.Enchantment.DURABILITY, 1, true);
        }

        // Always hide enchantments on grimoire items for clean appearance
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);

        result.setItemMeta(meta);

        // Update item flags to handle the case where regular enchantments might be added later
        result = updateItemFlags(result);

        return result;
    }

    /**
     * Update item flags based on current enchantment state
     * This method ensures that all enchantments are hidden on grimoire items for clean appearance
     *
     * @param item The item to update
     * @return The updated item
     */
    public ItemStack updateItemFlags(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return item;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        // Check if this item has a grimoire enchantment
        if (!hasEnchantment(item)) return item;

        // Always hide enchantments on grimoire items for clean appearance
        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Get the next rarity level
     *
     * @param currentRarity The current rarity
     * @return The next rarity level, or null if at max rarity
     */
    public String getNextRarity(String currentRarity) {
        if (currentRarity == null) return null;

        switch (currentRarity.toLowerCase()) {
            case "common":
                return "uncommon";
            case "uncommon":
                return "rare";
            case "rare":
                return "epic";
            case "epic":
                return "legendary";
            default:
                return null; // No higher rarity or invalid rarity
        }
    }

    /**
     * Check if two grimoire books can be combined
     *
     * @param book1 First grimoire book
     * @param book2 Second grimoire book
     * @return True if the books can be combined
     */
    public boolean canCombineGrimoires(ItemStack book1, ItemStack book2) {
        if (book1 == null || book2 == null) return false;
        if (book1.getType() != Material.ENCHANTED_BOOK || book2.getType() != Material.ENCHANTED_BOOK) return false;

        // Check if both have enchantments
        if (!hasEnchantment(book1) || !hasEnchantment(book2)) return false;

        // Get enchantment info
        String enchantId1 = getEnchantmentId(book1);
        String rarity1 = getEnchantmentRarity(book1);
        String enchantId2 = getEnchantmentId(book2);
        String rarity2 = getEnchantmentRarity(book2);

        // Check if enchantment IDs and rarities match
        if (enchantId1 == null || rarity1 == null || enchantId2 == null || rarity2 == null) return false;
        if (!enchantId1.equalsIgnoreCase(enchantId2)) return false;
        if (!rarity1.equalsIgnoreCase(rarity2)) return false;

        // Check if there's a higher rarity available
        return getNextRarity(rarity1) != null;
    }

    /**
     * Upgrade a grimoire book to the next rarity
     *
     * @param book The grimoire book to upgrade
     * @return The upgraded grimoire book, or null if upgrade failed
     */
    public ItemStack upgradeGrimoireRarity(ItemStack book) {
        if (book == null || book.getType() != Material.ENCHANTED_BOOK) return null;

        // Check if the book has an enchantment
        if (!hasEnchantment(book)) return null;

        // Get enchantment info
        String enchantId = getEnchantmentId(book);
        String currentRarity = getEnchantmentRarity(book);

        if (enchantId == null || currentRarity == null) return null;

        // Get the next rarity
        String nextRarity = getNextRarity(currentRarity);
        if (nextRarity == null) return null; // Already at max rarity

        // Get the enchantment
        GrimoireEnchantment enchantment = getEnchantment(enchantId);
        if (enchantment == null) return null;

        // Create a new book with the upgraded rarity
        return enchantment.createEnchantmentBook(nextRarity);
    }

    /**
     * Get a player's current mana
     *
     * @param player The player
     * @return The player's mana
     */
    public int getMana(Player player) {
        return playerMana.getOrDefault(player.getUniqueId(),
                getMaxMana(player));
    }

    /**
     * Set a player's mana
     *
     * @param player The player
     * @param mana The mana amount
     */
    public void setMana(Player player, int mana) {
        int maxMana = getMaxMana(player);
        playerMana.put(player.getUniqueId(), Math.min(mana, maxMana));
        updateManaDisplay(player);
    }

    /**
     * Get a player's maximum mana based on their active mana core
     *
     * @param player The player
     * @return The maximum mana value
     */
    public int getMaxMana(Player player) {
        if (manaCoreManager != null) {
            return manaCoreManager.getMaxMana(player);
        }
        return plugin.getConfig().getInt("Mana.Default-Mana", 100);
    }

    /**
     * Get a player's mana regeneration rate based on their active mana core
     *
     * @param player The player
     * @return The mana regeneration rate
     */
    public int getRegenRate(Player player) {
        if (manaCoreManager != null) {
            return manaCoreManager.getRegenRate(player);
        }
        return plugin.getConfig().getInt("Mana.Default-Regen", 2);
    }

    /**
     * Check if a player can use a specific rarity based on their active mana core
     *
     * @param player The player
     * @param rarity The rarity to check
     * @return True if the player can use the rarity
     */
    public boolean canUseRarity(Player player, String rarity) {
        if (manaCoreManager != null) {
            return manaCoreManager.canUseRarity(player, rarity);
        }
        return rarity.equals("common");
    }

    /**
     * Check if a player has enough mana
     *
     * @param player The player
     * @param amount The amount to check
     * @return True if the player has enough mana
     */
    public boolean hasMana(Player player, int amount) {
        return getMana(player) >= amount;
    }

    /**
     * Send a throttled mana message to a player
     *
     * @param player The player
     * @param message The message to send
     * @return True if the message was sent, false if throttled
     */
    public boolean sendThrottledManaMessage(Player player, String message) {
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        long throttleTime = plugin.getConfig().getLong("Mana.Message-Throttle", 250);

        // Check if we should throttle the message
        if (lastManaMessageTime.containsKey(uuid)) {
            long lastTime = lastManaMessageTime.get(uuid);
            if (currentTime - lastTime < throttleTime) {
                // Message is being throttled
                return false;
            }
        }

        // Send the message and update the last message time
        player.sendMessage(ColorUtils.process(
                plugin.getMessagesConfig().getString("General.Prefix", "") + message));
        lastManaMessageTime.put(uuid, currentTime);
        return true;
    }

    /**
     * Use mana for a player
     *
     * @param player The player
     * @param amount The amount to use
     * @return True if the mana was used successfully
     */
    public boolean useMana(Player player, int amount) {
        if (!hasMana(player, amount)) return false;

        setMana(player, getMana(player) - amount);
        return true;
    }

    /**
     * Update the mana display for a player
     *
     * @param player The player
     */
    private void updateManaDisplay(Player player) {
        int mana = getMana(player);
        int maxMana = getMaxMana(player);
        String displayTypeStr = plugin.getConfig().getString("Mana.Display-Type", "BOSS_BAR");
        ManaDisplayType displayType;

        try {
            displayType = ManaDisplayType.valueOf(displayTypeStr);
        } catch (IllegalArgumentException e) {
            displayType = ManaDisplayType.BOSS_BAR; // Default to boss bar if invalid
        }

        // Format the mana display text
        String manaText = ColorUtils.process(
                plugin.getConfig().getString("Mana.Mana-Display", "&7Mana &f{current}&7/{max}")
                        .replace("{current}", String.valueOf(mana))
                        .replace("{max}", String.valueOf(maxMana)));

        switch (displayType) {
            case BOSS_BAR:
                updateBossBarDisplay(player, mana, maxMana, manaText);
                break;
            case ACTION_BAR:
                updateActionBarDisplay(player, mana, maxMana, manaText);
                break;
            case NONE:
                // Remove any existing boss bar
                BossBar existingBar = manaBars.remove(player.getUniqueId());
                if (existingBar != null) {
                    existingBar.removeAll();
                }
                break;
        }
    }

    /**
     * Update the boss bar mana display for a player
     *
     * @param player The player
     * @param mana Current mana amount
     * @param maxMana Maximum mana amount
     * @param manaText Formatted mana text
     */
    private void updateBossBarDisplay(Player player, int mana, int maxMana, String manaText) {
        BossBar bar = manaBars.get(player.getUniqueId());
        if (bar == null) {
            bar = plugin.getServer().createBossBar(
                    manaText,
                    BarColor.BLUE,
                    BarStyle.SEGMENTED_10
            );
            manaBars.put(player.getUniqueId(), bar);
            bar.addPlayer(player);
        }

        bar.setProgress((double) mana / maxMana);
        bar.setTitle(manaText);

        // Only show bar when mana is being used
        if (mana < maxMana) {
            bar.setVisible(true);
        } else {
            bar.setVisible(false);
        }
    }

    /**
     * Update the action bar mana display for a player
     *
     * @param player The player
     * @param mana Current mana amount
     * @param maxMana Maximum mana amount
     * @param manaText Formatted mana text
     */
    private void updateActionBarDisplay(Player player, int mana, int maxMana, String manaText) {
        // Remove any existing boss bar
        BossBar existingBar = manaBars.remove(player.getUniqueId());
        if (existingBar != null) {
            existingBar.removeAll();
        }

        // Only show action bar when mana is being used
        if (mana < maxMana) {
            // Use TextComponent.fromLegacyText with the already processed text
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText(manaText));
        }
    }

    /**
     * Start the mana regeneration task
     */
    private void startManaRegenTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (UUID uuid : playerMana.keySet()) {
                    Player player = plugin.getServer().getPlayer(uuid);
                    if (player != null && player.isOnline()) {
                        int currentMana = playerMana.get(uuid);
                        int maxMana = getMaxMana(player);
                        int regenAmount = getRegenRate(player);

                        if (currentMana < maxMana) {
                            setMana(player, Math.min(currentMana + regenAmount, maxMana));
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // Run every second
    }

    /**
     * Handle player join event
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        playerMana.put(player.getUniqueId(), getMaxMana(player));

        // Fix any existing items with incorrect item flags
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            for (int i = 0; i < player.getInventory().getSize(); i++) {
                ItemStack item = player.getInventory().getItem(i);
                if (hasEnchantment(item)) {
                    ItemStack updatedItem = updateItemFlags(item);
                    if (!updatedItem.equals(item)) {
                        player.getInventory().setItem(i, updatedItem);
                    }
                }
            }
        }, 20L); // Run 1 second after join to ensure player is fully loaded
    }

    /**
     * Handle player quit event
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();

        playerMana.remove(uuid);

        BossBar bar = manaBars.remove(uuid);
        if (bar != null) {
            bar.removeAll();
        }
    }

    /**
     * Handle player interact event for activating enchantments
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        // Check if the item has an enchantment
        if (hasEnchantment(item)) {
            String enchantId = getEnchantmentId(item);
            String rarity = getEnchantmentRarity(item);

            if (enchantId != null && rarity != null) {
                // Don't activate if it's an enchantment book
                if (item.getType() != Material.ENCHANTED_BOOK) {
                    GrimoireEnchantment enchantment = getEnchantment(enchantId);

                    if (enchantment != null) {
                        // Check if right-click action
                        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
                            // Don't activate when interacting with blocks that have inventories
                            if (event.getAction() == Action.RIGHT_CLICK_BLOCK && event.getClickedBlock() != null) {
                                Material blockType = event.getClickedBlock().getType();
                                // Check if the block is an inventory block
                                if (blockType.isInteractable()) {
                                    // Don't activate the enchantment when interacting with inventory blocks
                                    return;
                                }
                            }

                            // Check if the player can use this rarity based on their mana core
                            if (!canUseRarity(player, rarity)) {
                                String incompatibleRarityMsg = plugin.getMessagesConfig().getString("ManaCores.IncompatibleRarity",
                                        "&#FF6347ʏᴏᴜʀ ᴄᴜʀʀᴇɴᴛ ᴍᴀɴᴀ ᴄᴏʀᴇ ᴄᴀɴɴᴏᴛ ᴜsᴇ &#FFFFFF{rarity} &#FF6347ɢʀɪᴍᴏɪʀᴇs.")
                                        .replace("{rarity}", plugin.getConfig().getString("Rarity." + rarity, rarity));
                                player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + incompatibleRarityMsg));
                                return;
                            }

                            // Activate the enchantment
                            enchantment.activate(player, item, rarity);
                        }
                    }
                }
            }
        }
    }

    /**
     * Handle inventory click event for applying enchantment books
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        ItemStack cursor = event.getCursor(); // Item being held by cursor
        ItemStack current = event.getCurrentItem(); // Item being clicked on

        // Check if player is holding an enchantment book and clicking on another enchantment book (for combining)
        if (cursor != null && cursor.getType() == Material.ENCHANTED_BOOK && hasEnchantment(cursor) &&
            current != null && current.getType() == Material.ENCHANTED_BOOK && hasEnchantment(current)) {

            // Check if the books can be combined
            if (canCombineGrimoires(cursor, current)) {
                // Cancel the event to prevent normal item placement
                event.setCancelled(true);

                // Open the combine confirmation GUI
                plugin.getGuiManager().openCombineGUI(player, cursor, current, event.getSlot(), -1, event.getClickedInventory());
                return;
            } else {
                // Books cannot be combined, send message
                String incompatibleMsg = plugin.getMessagesConfig().getString("Enchantment.IncompatibleGrimoires",
                        "&#FF6347ᴛʜᴇsᴇ ɢʀɪᴍᴏɪʀᴇs ᴄᴀɴɴᴏᴛ ʙᴇ ᴄᴏᴍʙɪɴᴇᴅ. ᴛʜᴇʏ ᴍᴜsᴛ ʜᴀᴠᴇ ᴛʜᴇ sᴀᴍᴇ sᴘᴇʟʟ ᴀɴᴅ ʀᴀʀɪᴛʏ.");
                player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + incompatibleMsg));
                event.setCancelled(true);
                return;
            }
        }

        // Check if player is holding an enchantment book and clicking on an item
        if (cursor != null && cursor.getType() == Material.ENCHANTED_BOOK && hasEnchantment(cursor) &&
            current != null && current.getType() != Material.AIR && current.getType() != Material.ENCHANTED_BOOK) {

            // Get enchantment info
            String enchantId = getEnchantmentId(cursor);
            String rarity = getEnchantmentRarity(cursor);

            if (enchantId != null && rarity != null) {
                // Cancel the event to prevent normal item placement
                event.setCancelled(true);

                // Check if the target item already has a Grimoire enchantment
                if (hasEnchantment(current)) {
                    // Item already has an enchantment, send message and return
                    String alreadyEnchantedMsg = plugin.getMessagesConfig().getString("Enchantment.AlreadyEnchanted", "&#FF6347ᴛʜɪs ɪᴛᴇᴍ ᴀʟʀᴇᴀᴅʏ ʜᴀs ᴀ ɢʀɪᴍᴏɪʀᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ!");
                    player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + alreadyEnchantedMsg));
                    return;
                }

                // Get the enchantment and check if the item is in the whitelist
                GrimoireEnchantment enchantment = getEnchantment(enchantId);
                if (enchantment != null && !enchantment.isItemInWhitelist(current)) {
                    // Item is not in the whitelist, send message and return
                    String invalidItemMsg = plugin.getMessagesConfig().getString("Enchantment.InvalidItem", "&#FF6347ᴛʜɪs ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ ᴄᴀɴɴᴏᴛ ʙᴇ ᴀᴘᴘʟɪᴇᴅ ᴛᴏ ᴛʜɪs ɪᴛᴇᴍ!");
                    player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + invalidItemMsg));
                    return;
                }

                // Store the slot information for later use
                int slot = event.getSlot();
                int slotType = event.getSlotType().ordinal();

                // Make deep clones to avoid reference issues
                ItemStack cursorClone = cursor.clone();
                ItemStack currentClone = current.clone();

                // Open confirmation GUI with the book and target item
                plugin.getGuiManager().openConfirmationGUI(player, cursorClone, currentClone, slot, slotType, event.getClickedInventory());
            }
        }
    }

    /**
     * Handle entity damage event for enchantment triggers
     *
     * Note: We no longer activate grimoire enchantments on entity damage
     * as this was causing unintended activations
     */
    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Removed activation on hit to prevent unintended activations
    }

    /**
     * Handle item held change event for updating mana display
     */
    @EventHandler
    public void onItemHeldChange(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItem(event.getNewSlot());

        // Update mana display if the item has an enchantment
        if (hasEnchantment(item)) {
            updateManaDisplay(player);
            // Update item flags in case regular enchantments were added
            ItemStack updatedItem = updateItemFlags(item);
            if (!updatedItem.equals(item)) {
                player.getInventory().setItem(event.getNewSlot(), updatedItem);
            }
        }
    }

    /**
     * Handle inventory click event for updating item flags
     */
    @EventHandler(priority = org.bukkit.event.EventPriority.MONITOR)
    public void onInventoryClickForFlagUpdate(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();

        // Schedule a task to run after the click event is processed
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            // Check all items in player's inventory for grimoire enchantments that need flag updates
            for (int i = 0; i < player.getInventory().getSize(); i++) {
                ItemStack item = player.getInventory().getItem(i);
                if (hasEnchantment(item)) {
                    ItemStack updatedItem = updateItemFlags(item);
                    if (!updatedItem.equals(item)) {
                        player.getInventory().setItem(i, updatedItem);
                    }
                }
            }
        }, 1L); // Run 1 tick later
    }

    /**
     * Handle anvil prepare event to prevent grimoire items from being used in anvils
     */
    @EventHandler
    public void onPrepareAnvil(PrepareAnvilEvent event) {
        // Check if any of the input items are grimoire items
        ItemStack firstItem = event.getInventory().getItem(0);
        ItemStack secondItem = event.getInventory().getItem(1);

        // If either input item has a grimoire enchantment, cancel the anvil operation
        if ((firstItem != null && hasEnchantment(firstItem)) ||
            (secondItem != null && hasEnchantment(secondItem))) {
            event.setResult(null); // Cancel the anvil operation
        }
    }
}
